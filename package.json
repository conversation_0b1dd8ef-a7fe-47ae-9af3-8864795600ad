{"name": "park", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@gongyinshi/kit": "^0.0.8", "@next/third-parties": "^14.1.3", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-slot": "^1.0.2", "@sentry/nextjs": "^7.108.0", "@strapi/blocks-react-renderer": "^1.0.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "flowbite-react": "^0.10.2", "lodash-es": "^4.17.21", "lucide-react": "^0.336.0", "md5": "^2.3.0", "mime-types": "^2.1.35", "next": "14.1.0", "next-intl": "^3.9.1", "react": "^18", "react-device-detect": "^2.2.3", "react-dom": "^18", "sharp": "^0.33.2", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@lobehub/i18n-cli": "^1.18.0", "@tailwindcss/typography": "^0.5.10", "@types/lodash-es": "^4.17.12", "@types/md5": "^2.3.5", "@types/mime-types": "^2.1.4", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}